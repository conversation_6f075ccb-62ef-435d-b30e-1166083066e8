<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>暂无可用收银台 - 缺省图标设计方案</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .card {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            text-align: center;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .card h3 {
            font-size: 1.3rem;
            margin-bottom: 15px;
            color: #333;
            font-weight: 500;
        }

        .card p {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .icon-container {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
        }

        .copy-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background 0.3s ease;
        }

        .copy-btn:hover {
            background: #5a6fd8;
        }

        .copy-btn:active {
            background: #4c63d2;
        }

        .usage-note {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 25px;
            margin-top: 30px;
            color: #333;
        }

        .usage-note h3 {
            margin-bottom: 15px;
            color: #667eea;
        }

        .usage-note ul {
            list-style-position: inside;
            line-height: 1.6;
        }

        .usage-note li {
            margin-bottom: 8px;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>暂无可用收银台</h1>
            <p>缺省图标设计方案 - 5种不同风格供您选择</p>
        </div>

        <div class="grid">
            <!-- 设计方案1：简约线条风格 -->
            <div class="card">
                <h3>方案一：简约线条风格</h3>
                <p>使用虚线边框和禁用符号，简洁明了地表达"暂无收银台"的状态</p>
                <div class="icon-container">
                    <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="60" cy="60" r="60" fill="#F5F5F5" stroke="#E0E0E0" stroke-width="2"/>
                        <rect x="25" y="45" width="70" height="40" rx="6" fill="none" stroke="#BDBDBD" stroke-width="2" stroke-dasharray="4,4"/>
                        <rect x="35" y="52" width="20" height="15" rx="2" fill="none" stroke="#BDBDBD" stroke-width="1.5"/>
                        <rect x="60" y="52" width="25" height="26" rx="2" fill="none" stroke="#BDBDBD" stroke-width="1.5"/>
                        <circle cx="67" cy="60" r="1.5" fill="#BDBDBD"/>
                        <circle cx="72" cy="60" r="1.5" fill="#BDBDBD"/>
                        <circle cx="77" cy="60" r="1.5" fill="#BDBDBD"/>
                        <circle cx="67" cy="65" r="1.5" fill="#BDBDBD"/>
                        <circle cx="72" cy="65" r="1.5" fill="#BDBDBD"/>
                        <circle cx="77" cy="65" r="1.5" fill="#BDBDBD"/>
                        <circle cx="67" cy="70" r="1.5" fill="#BDBDBD"/>
                        <circle cx="72" cy="70" r="1.5" fill="#BDBDBD"/>
                        <circle cx="77" cy="70" r="1.5" fill="#BDBDBD"/>
                        <circle cx="60" cy="60" r="35" fill="none" stroke="#FF6B6B" stroke-width="3" opacity="0.8"/>
                        <line x1="35" y1="35" x2="85" y2="85" stroke="#FF6B6B" stroke-width="3" stroke-linecap="round" opacity="0.8"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(1)">复制 SVG 代码</button>
            </div>

            <!-- 设计方案2：卡片风格 -->
            <div class="card">
                <h3>方案二：卡片风格</h3>
                <p>采用卡片式设计，底部配有状态提示，整体风格温和友好</p>
                <div class="icon-container">
                    <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect width="120" height="120" rx="12" fill="#FAFAFA"/>
                        <rect x="20" y="30" width="80" height="60" rx="8" fill="white" stroke="#E8E8E8" stroke-width="1"/>
                        <rect x="35" y="45" width="50" height="30" rx="4" fill="#F0F0F0" stroke="#D0D0D0" stroke-width="1"/>
                        <rect x="40" y="50" width="15" height="10" rx="1" fill="white" stroke="#D0D0D0" stroke-width="1"/>
                        <rect x="58" y="50" width="22" height="20" rx="2" fill="white" stroke="#D0D0D0" stroke-width="1"/>
                        <rect x="61" y="53" width="4" height="3" rx="0.5" fill="#E0E0E0"/>
                        <rect x="66" y="53" width="4" height="3" rx="0.5" fill="#E0E0E0"/>
                        <rect x="71" y="53" width="4" height="3" rx="0.5" fill="#E0E0E0"/>
                        <rect x="61" y="57" width="4" height="3" rx="0.5" fill="#E0E0E0"/>
                        <rect x="66" y="57" width="4" height="3" rx="0.5" fill="#E0E0E0"/>
                        <rect x="71" y="57" width="4" height="3" rx="0.5" fill="#E0E0E0"/>
                        <rect x="61" y="61" width="4" height="3" rx="0.5" fill="#E0E0E0"/>
                        <rect x="66" y="61" width="4" height="3" rx="0.5" fill="#E0E0E0"/>
                        <rect x="71" y="61" width="4" height="3" rx="0.5" fill="#E0E0E0"/>
                        <rect x="61" y="65" width="14" height="3" rx="0.5" fill="#E0E0E0"/>
                        <circle cx="60" cy="100" r="8" fill="#FFE0E0"/>
                        <path d="M56 100 L60 96 L64 100 L60 104 Z" fill="#FF6B6B"/>
                        <line x1="30" y1="105" x2="50" y2="105" stroke="#E0E0E0" stroke-width="2" stroke-linecap="round"/>
                        <line x1="70" y1="105" x2="90" y2="105" stroke="#E0E0E0" stroke-width="2" stroke-linecap="round"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(2)">复制 SVG 代码</button>
            </div>

            <!-- 设计方案3：插画风格 -->
            <div class="card">
                <h3>方案三：插画风格</h3>
                <p>使用渐变背景和云朵元素，营造温馨的插画氛围</p>
                <div class="icon-container">
                    <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#F8F9FA;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#E9ECEF;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <rect width="120" height="120" rx="16" fill="url(#bg)"/>
                        <ellipse cx="60" cy="85" rx="45" ry="8" fill="#DEE2E6" opacity="0.6"/>
                        <rect x="30" y="50" width="60" height="35" rx="6" fill="#F8F9FA" stroke="#CED4DA" stroke-width="2"/>
                        <rect x="38" y="58" width="18" height="12" rx="2" fill="#343A40" stroke="#6C757D" stroke-width="1"/>
                        <rect x="40" y="60" width="14" height="8" rx="1" fill="#495057"/>
                        <rect x="60" y="58" width="25" height="20" rx="3" fill="white" stroke="#CED4DA" stroke-width="1"/>
                        <g fill="#E9ECEF" stroke="#CED4DA" stroke-width="0.5">
                            <rect x="63" y="61" width="3" height="2" rx="0.5"/>
                            <rect x="67" y="61" width="3" height="2" rx="0.5"/>
                            <rect x="71" y="61" width="3" height="2" rx="0.5"/>
                            <rect x="75" y="61" width="3" height="2" rx="0.5"/>
                            <rect x="79" y="61" width="3" height="2" rx="0.5"/>
                            <rect x="63" y="64" width="3" height="2" rx="0.5"/>
                            <rect x="67" y="64" width="3" height="2" rx="0.5"/>
                            <rect x="71" y="64" width="3" height="2" rx="0.5"/>
                            <rect x="75" y="64" width="3" height="2" rx="0.5"/>
                            <rect x="79" y="64" width="3" height="2" rx="0.5"/>
                            <rect x="63" y="67" width="3" height="2" rx="0.5"/>
                            <rect x="67" y="67" width="3" height="2" rx="0.5"/>
                            <rect x="71" y="67" width="3" height="2" rx="0.5"/>
                            <rect x="75" y="67" width="3" height="2" rx="0.5"/>
                            <rect x="79" y="67" width="3" height="2" rx="0.5"/>
                            <rect x="63" y="70" width="19" height="2" rx="0.5"/>
                        </g>
                        <g transform="translate(45, 25)">
                            <ellipse cx="15" cy="12" rx="8" ry="6" fill="#ADB5BD" opacity="0.7"/>
                            <ellipse cx="8" cy="15" rx="6" ry="4" fill="#ADB5BD" opacity="0.7"/>
                            <ellipse cx="22" cy="15" rx="6" ry="4" fill="#ADB5BD" opacity="0.7"/>
                            <ellipse cx="15" cy="18" rx="12" ry="3" fill="#ADB5BD" opacity="0.7"/>
                        </g>
                        <circle cx="60" cy="30" r="6" fill="white" stroke="#6C757D" stroke-width="1.5"/>
                        <path d="M57 27 Q60 24 63 27 Q63 29 60 30 M60 32 L60 33" stroke="#6C757D" stroke-width="1.5" fill="none" stroke-linecap="round"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(3)">复制 SVG 代码</button>
            </div>

            <!-- 设计方案4：极简几何风格 -->
            <div class="card">
                <h3>方案四：极简几何风格</h3>
                <p>采用几何图形和网格设计，体现现代简约美学</p>
                <div class="icon-container">
                    <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect width="120" height="120" rx="20" fill="#FAFBFC"/>
                        <rect x="25" y="40" width="70" height="45" rx="8" fill="none" stroke="#E1E5E9" stroke-width="3" stroke-dasharray="8,4"/>
                        <rect x="35" y="50" width="20" height="15" rx="3" fill="#F1F3F4"/>
                        <rect x="60" y="50" width="25" height="25" rx="4" fill="#F1F3F4"/>
                        <g fill="#D1D5DB">
                            <circle cx="67" cy="57" r="1"/>
                            <circle cx="72" cy="57" r="1"/>
                            <circle cx="77" cy="57" r="1"/>
                            <circle cx="67" cy="62" r="1"/>
                            <circle cx="72" cy="62" r="1"/>
                            <circle cx="77" cy="62" r="1"/>
                            <circle cx="67" cy="67" r="1"/>
                            <circle cx="72" cy="67" r="1"/>
                            <circle cx="77" cy="67" r="1"/>
                        </g>
                        <circle cx="60" cy="62" r="25" fill="rgba(255,255,255,0.9)" stroke="#E1E5E9" stroke-width="2"/>
                        <path d="M50 62 L70 62 M60 52 L60 72" stroke="#9CA3AF" stroke-width="2" stroke-linecap="round" opacity="0.6"/>
                        <g transform="translate(60, 95)">
                            <circle r="3" fill="#FEE2E2"/>
                            <circle r="1.5" fill="#EF4444"/>
                        </g>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(4)">复制 SVG 代码</button>
            </div>

            <!-- 设计方案5：立体风格 -->
            <div class="card">
                <h3>方案五：立体风格</h3>
                <p>运用阴影和立体效果，增强视觉层次感和真实感</p>
                <div class="icon-container">
                    <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="shadow" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#000000;stop-opacity:0.1" />
                                <stop offset="100%" style="stop-color:#000000;stop-opacity:0.05" />
                            </linearGradient>
                        </defs>
                        <rect width="120" height="120" rx="16" fill="#F8FAFC"/>
                        <rect x="32" y="52" width="60" height="35" rx="6" fill="url(#shadow)"/>
                        <rect x="30" y="50" width="60" height="35" rx="6" fill="white" stroke="#E2E8F0" stroke-width="1"/>
                        <rect x="38" y="58" width="18" height="12" rx="2" fill="#1E293B" stroke="#334155" stroke-width="1"/>
                        <rect x="40" y="60" width="14" height="8" rx="1" fill="#0F172A"/>
                        <rect x="60" y="58" width="25" height="20" rx="3" fill="#F1F5F9" stroke="#CBD5E1" stroke-width="1"/>
                        <g>
                            <rect x="63" y="61" width="3" height="2" rx="0.5" fill="white" stroke="#CBD5E1" stroke-width="0.5"/>
                            <rect x="67" y="61" width="3" height="2" rx="0.5" fill="white" stroke="#CBD5E1" stroke-width="0.5"/>
                            <rect x="71" y="61" width="3" height="2" rx="0.5" fill="white" stroke="#CBD5E1" stroke-width="0.5"/>
                            <rect x="75" y="61" width="3" height="2" rx="0.5" fill="white" stroke="#CBD5E1" stroke-width="0.5"/>
                            <rect x="79" y="61" width="3" height="2" rx="0.5" fill="white" stroke="#CBD5E1" stroke-width="0.5"/>
                        </g>
                        <circle cx="60" cy="30" r="15" fill="#FEF2F2" stroke="#FECACA" stroke-width="2"/>
                        <path d="M55 25 L65 35 M65 25 L55 35" stroke="#EF4444" stroke-width="2" stroke-linecap="round"/>
                        <rect x="35" y="90" width="50" height="2" rx="1" fill="#E2E8F0"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(5)">复制 SVG 代码</button>
            </div>
        </div>

        <div class="usage-note">
            <h3>使用说明</h3>
            <ul>
                <li><strong>Figma 导入：</strong>复制 SVG 代码后，在 Figma 中粘贴即可直接使用</li>
                <li><strong>颜色调整：</strong>所有颜色值都可以根据您的品牌色彩进行调整</li>
                <li><strong>尺寸缩放：</strong>SVG 格式支持无损缩放，适配各种尺寸需求</li>
                <li><strong>样式建议：</strong>建议选择与您现有 UI 风格最匹配的方案</li>
                <li><strong>交互提示：</strong>可配合文字说明"暂无可用收银台，请先创建收银台"使用</li>
            </ul>
        </div>
    </div>

    <script>
        // SVG 代码数组
        const svgCodes = {
            1: `<svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景圆 -->
  <circle cx="60" cy="60" r="60" fill="#F5F5F5" stroke="#E0E0E0" stroke-width="2"/>

  <!-- 收银台轮廓 -->
  <rect x="25" y="45" width="70" height="40" rx="6" fill="none" stroke="#BDBDBD" stroke-width="2" stroke-dasharray="4,4"/>

  <!-- 屏幕 -->
  <rect x="35" y="52" width="20" height="15" rx="2" fill="none" stroke="#BDBDBD" stroke-width="1.5"/>

  <!-- 键盘区域 -->
  <rect x="60" y="52" width="25" height="26" rx="2" fill="none" stroke="#BDBDBD" stroke-width="1.5"/>
  <circle cx="67" cy="60" r="1.5" fill="#BDBDBD"/>
  <circle cx="72" cy="60" r="1.5" fill="#BDBDBD"/>
  <circle cx="77" cy="60" r="1.5" fill="#BDBDBD"/>
  <circle cx="67" cy="65" r="1.5" fill="#BDBDBD"/>
  <circle cx="72" cy="65" r="1.5" fill="#BDBDBD"/>
  <circle cx="77" cy="65" r="1.5" fill="#BDBDBD"/>
  <circle cx="67" cy="70" r="1.5" fill="#BDBDBD"/>
  <circle cx="72" cy="70" r="1.5" fill="#BDBDBD"/>
  <circle cx="77" cy="70" r="1.5" fill="#BDBDBD"/>

  <!-- 禁用符号 -->
  <circle cx="60" cy="60" r="35" fill="none" stroke="#FF6B6B" stroke-width="3" opacity="0.8"/>
  <line x1="35" y1="35" x2="85" y2="85" stroke="#FF6B6B" stroke-width="3" stroke-linecap="round" opacity="0.8"/>
</svg>`,

            2: `<svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="120" height="120" rx="12" fill="#FAFAFA"/>

  <!-- 卡片背景 -->
  <rect x="20" y="30" width="80" height="60" rx="8" fill="white" stroke="#E8E8E8" stroke-width="1"/>

  <!-- 收银台图标 -->
  <rect x="35" y="45" width="50" height="30" rx="4" fill="#F0F0F0" stroke="#D0D0D0" stroke-width="1"/>

  <!-- 屏幕 -->
  <rect x="40" y="50" width="15" height="10" rx="1" fill="white" stroke="#D0D0D0" stroke-width="1"/>

  <!-- 按键 -->
  <rect x="58" y="50" width="22" height="20" rx="2" fill="white" stroke="#D0D0D0" stroke-width="1"/>
  <rect x="61" y="53" width="4" height="3" rx="0.5" fill="#E0E0E0"/>
  <rect x="66" y="53" width="4" height="3" rx="0.5" fill="#E0E0E0"/>
  <rect x="71" y="53" width="4" height="3" rx="0.5" fill="#E0E0E0"/>
  <rect x="61" y="57" width="4" height="3" rx="0.5" fill="#E0E0E0"/>
  <rect x="66" y="57" width="4" height="3" rx="0.5" fill="#E0E0E0"/>
  <rect x="71" y="57" width="4" height="3" rx="0.5" fill="#E0E0E0"/>
  <rect x="61" y="61" width="4" height="3" rx="0.5" fill="#E0E0E0"/>
  <rect x="66" y="61" width="4" height="3" rx="0.5" fill="#E0E0E0"/>
  <rect x="71" y="61" width="4" height="3" rx="0.5" fill="#E0E0E0"/>
  <rect x="61" y="65" width="14" height="3" rx="0.5" fill="#E0E0E0"/>

  <!-- 空状态图标 -->
  <circle cx="60" cy="100" r="8" fill="#FFE0E0"/>
  <path d="M56 100 L60 96 L64 100 L60 104 Z" fill="#FF6B6B"/>

  <!-- 文字提示线条 -->
  <line x1="30" y1="105" x2="50" y2="105" stroke="#E0E0E0" stroke-width="2" stroke-linecap="round"/>
  <line x1="70" y1="105" x2="90" y2="105" stroke="#E0E0E0" stroke-width="2" stroke-linecap="round"/>
</svg>`,

            3: `<svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景渐变 -->
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F8F9FA;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E9ECEF;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="120" height="120" rx="16" fill="url(#bg)"/>

  <!-- 桌面 -->
  <ellipse cx="60" cy="85" rx="45" ry="8" fill="#DEE2E6" opacity="0.6"/>

  <!-- 收银台主体 -->
  <rect x="30" y="50" width="60" height="35" rx="6" fill="#F8F9FA" stroke="#CED4DA" stroke-width="2"/>

  <!-- 屏幕 -->
  <rect x="38" y="58" width="18" height="12" rx="2" fill="#343A40" stroke="#6C757D" stroke-width="1"/>
  <rect x="40" y="60" width="14" height="8" rx="1" fill="#495057"/>

  <!-- 键盘 -->
  <rect x="60" y="58" width="25" height="20" rx="3" fill="white" stroke="#CED4DA" stroke-width="1"/>

  <!-- 按键网格 -->
  <g fill="#E9ECEF" stroke="#CED4DA" stroke-width="0.5">
    <rect x="63" y="61" width="3" height="2" rx="0.5"/>
    <rect x="67" y="61" width="3" height="2" rx="0.5"/>
    <rect x="71" y="61" width="3" height="2" rx="0.5"/>
    <rect x="75" y="61" width="3" height="2" rx="0.5"/>
    <rect x="79" y="61" width="3" height="2" rx="0.5"/>

    <rect x="63" y="64" width="3" height="2" rx="0.5"/>
    <rect x="67" y="64" width="3" height="2" rx="0.5"/>
    <rect x="71" y="64" width="3" height="2" rx="0.5"/>
    <rect x="75" y="64" width="3" height="2" rx="0.5"/>
    <rect x="79" y="64" width="3" height="2" rx="0.5"/>

    <rect x="63" y="67" width="3" height="2" rx="0.5"/>
    <rect x="67" y="67" width="3" height="2" rx="0.5"/>
    <rect x="71" y="67" width="3" height="2" rx="0.5"/>
    <rect x="75" y="67" width="3" height="2" rx="0.5"/>
    <rect x="79" y="67" width="3" height="2" rx="0.5"/>

    <rect x="63" y="70" width="19" height="2" rx="0.5"/>
  </g>

  <!-- 空状态云朵 -->
  <g transform="translate(45, 25)">
    <ellipse cx="15" cy="12" rx="8" ry="6" fill="#ADB5BD" opacity="0.7"/>
    <ellipse cx="8" cy="15" rx="6" ry="4" fill="#ADB5BD" opacity="0.7"/>
    <ellipse cx="22" cy="15" rx="6" ry="4" fill="#ADB5BD" opacity="0.7"/>
    <ellipse cx="15" cy="18" rx="12" ry="3" fill="#ADB5BD" opacity="0.7"/>
  </g>

  <!-- 问号 -->
  <circle cx="60" cy="30" r="6" fill="white" stroke="#6C757D" stroke-width="1.5"/>
  <path d="M57 27 Q60 24 63 27 Q63 29 60 30 M60 32 L60 33" stroke="#6C757D" stroke-width="1.5" fill="none" stroke-linecap="round"/>
</svg>`,

            4: `<svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="120" height="120" rx="20" fill="#FAFBFC"/>

  <!-- 主要形状 -->
  <rect x="25" y="40" width="70" height="45" rx="8" fill="none" stroke="#E1E5E9" stroke-width="3" stroke-dasharray="8,4"/>

  <!-- 内部元素 -->
  <rect x="35" y="50" width="20" height="15" rx="3" fill="#F1F3F4"/>
  <rect x="60" y="50" width="25" height="25" rx="4" fill="#F1F3F4"/>

  <!-- 网格点 -->
  <g fill="#D1D5DB">
    <circle cx="67" cy="57" r="1"/>
    <circle cx="72" cy="57" r="1"/>
    <circle cx="77" cy="57" r="1"/>
    <circle cx="67" cy="62" r="1"/>
    <circle cx="72" cy="62" r="1"/>
    <circle cx="77" cy="62" r="1"/>
    <circle cx="67" cy="67" r="1"/>
    <circle cx="72" cy="67" r="1"/>
    <circle cx="77" cy="67" r="1"/>
  </g>

  <!-- 中心空状态图标 -->
  <circle cx="60" cy="62" r="25" fill="rgba(255,255,255,0.9)" stroke="#E1E5E9" stroke-width="2"/>
  <path d="M50 62 L70 62 M60 52 L60 72" stroke="#9CA3AF" stroke-width="2" stroke-linecap="round" opacity="0.6"/>

  <!-- 底部提示点 -->
  <g transform="translate(60, 95)">
    <circle r="3" fill="#FEE2E2"/>
    <circle r="1.5" fill="#EF4444"/>
  </g>
</svg>`,

            5: `<svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="shadow" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#000000;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:#000000;stop-opacity:0.05" />
    </linearGradient>
  </defs>

  <!-- 背景 -->
  <rect width="120" height="120" rx="16" fill="#F8FAFC"/>

  <!-- 阴影 -->
  <rect x="32" y="52" width="60" height="35" rx="6" fill="url(#shadow)"/>

  <!-- 收银台主体 -->
  <rect x="30" y="50" width="60" height="35" rx="6" fill="white" stroke="#E2E8F0" stroke-width="1"/>

  <!-- 屏幕区域 -->
  <rect x="38" y="58" width="18" height="12" rx="2" fill="#1E293B" stroke="#334155" stroke-width="1"/>
  <rect x="40" y="60" width="14" height="8" rx="1" fill="#0F172A"/>

  <!-- 键盘区域 -->
  <rect x="60" y="58" width="25" height="20" rx="3" fill="#F1F5F9" stroke="#CBD5E1" stroke-width="1"/>

  <!-- 3D按键效果 -->
  <g>
    <rect x="63" y="61" width="3" height="2" rx="0.5" fill="white" stroke="#CBD5E1" stroke-width="0.5"/>
    <rect x="67" y="61" width="3" height="2" rx="0.5" fill="white" stroke="#CBD5E1" stroke-width="0.5"/>
    <rect x="71" y="61" width="3" height="2" rx="0.5" fill="white" stroke="#CBD5E1" stroke-width="0.5"/>
    <rect x="75" y="61" width="3" height="2" rx="0.5" fill="white" stroke="#CBD5E1" stroke-width="0.5"/>
    <rect x="79" y="61" width="3" height="2" rx="0.5" fill="white" stroke="#CBD5E1" stroke-width="0.5"/>
  </g>

  <!-- 空状态指示器 -->
  <circle cx="60" cy="30" r="15" fill="#FEF2F2" stroke="#FECACA" stroke-width="2"/>
  <path d="M55 25 L65 35 M65 25 L55 35" stroke="#EF4444" stroke-width="2" stroke-linecap="round"/>

  <!-- 底部装饰线 -->
  <rect x="35" y="90" width="50" height="2" rx="1" fill="#E2E8F0"/>
</svg>`
        };

        // 复制 SVG 代码到剪贴板
        function copySVG(designNumber) {
            const svgCode = svgCodes[designNumber];

            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(svgCode).then(() => {
                    showCopySuccess(designNumber);
                }).catch(err => {
                    console.error('复制失败:', err);
                    fallbackCopy(svgCode, designNumber);
                });
            } else {
                fallbackCopy(svgCode, designNumber);
            }
        }

        // 备用复制方法
        function fallbackCopy(text, designNumber) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                document.execCommand('copy');
                showCopySuccess(designNumber);
            } catch (err) {
                console.error('复制失败:', err);
                alert('复制失败，请手动选择并复制 SVG 代码');
            }

            document.body.removeChild(textArea);
        }

        // 显示复制成功提示
        function showCopySuccess(designNumber) {
            const button = document.querySelector(`button[onclick="copySVG(${designNumber})"]`);
            const originalText = button.textContent;

            button.textContent = '已复制！';
            button.style.background = '#10B981';

            setTimeout(() => {
                button.textContent = originalText;
                button.style.background = '#667eea';
            }, 2000);
        }
    </script>
</body>
</html>
